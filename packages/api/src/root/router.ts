import { changeUserStatus } from "./mutations/changeUserStatus"
import { configureCommunity } from "./mutations/configureCommunity"
import { createCommunity } from "./mutations/createCommunity"
import { createInvite } from "./mutations/createInvite"
import { joinByInvite } from "./mutations/joinByInvite"
import { joinCommunity } from "./mutations/joinCommunity"
import { leaveCommunity } from "./mutations/leaveCommunity"
import { switchGameShare } from "./mutations/switchGameShare"
import { userProfileUpdate } from "./mutations/userProfileUpdate"
import { communityBasic } from "./queries/communityBasic"
import { communityExpanded2 } from "./queries/communityExpanded2"
import { communityGameExtended2 } from "./queries/communityGameExtended2"
import { communityGamesList2 } from "./queries/communityGamesList2"
import { communityList } from "./queries/communityList"
import { communityUserInfo } from "./queries/communityUserInfo"
import { communityUserList } from "./queries/communityUserList"
import { getMyGameInfo } from "./queries/getMyGameInfo"
import { getMyInfo } from "./queries/getMyInfo"
import { joinByInvitePrejoin } from "./queries/joinByInvitePrejoin"
import { loggedInUser } from "./queries/loggedInUser"
import { logoutUser } from "./queries/logoutUser"
import { publicCommunityList } from "./queries/publicCommunityList"
import { tagList } from "./queries/tagList"
import { router } from "./queries/trpc"

export const appRouter = router({
  logoutUser,
  communityList,
  loggedInUser,
  communityExpanded2,
  communityGamesList2,
  communityBasic,
  communityGameExtended2,
  communityUserList,
  communityUserInfo,
  leaveCommunity,
  userProfileUpdate,
  getMyInfo,
  publicCommunityList,
  joinCommunity,
  joinByInvite,
  changeUserStatus,
  createCommunity,
  configureCommunity,
  createInvite,
  switchGameShare,
  getMyGameInfo,
  tagList,
  joinByInvitePrejoin,
}) // ...

export type AppRouter = typeof appRouter
