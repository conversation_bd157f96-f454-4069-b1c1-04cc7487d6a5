import { User, getAuth } from "firebase/auth"
import { useEffect, useState } from "react"

import { firebaseApp } from "../utils/firebase"

export const useFirebase = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [getAccessTokenSilently, setGetAccessTokenSilently] = useState(
    () => async (): Promise<string | null> => null,
  )

  useEffect(() => {
    const firebaseAuth = getAuth(firebaseApp)
    const unsubscribe = firebaseAuth.onAuthStateChanged(async (user) => {
      if (user) {
        setIsAuthenticated(true)
        setGetAccessTokenSilently(() => async () => user.getIdToken())
      } else {
        setIsAuthenticated(false)
        setGetAccessTokenSilently(() => async () => null)
      }

      setUser(user)
    })

    return () => unsubscribe()
  }, [])

  return { isAuthenticated, getAccessTokenSilently, user }
}
