import { type User } from "firebase/auth"
import { useCallback, useEffect, useState } from "react"

import { useUserStore } from "../store/useUserStore"
import { RouterOutput, trpc } from "../trpc/trpc"
import { auth as authStorage } from "../utils/access"

import { useFirebase } from "./useFirebase"

type UserDataLoaderOutput = RouterOutput["loggedInUser"]

export const useUserDataLoader = () => {
  const { setUserData, setUserLoaded, clearUserData } = useUserStore()
  const [isLoading, setIsLoading] = useState(true)
  const [user, setUser] = useState<User | null>(null)
  const [fetchCount, setFetchCount] = useState(0)
  const { isAuthenticated, user: firebaseUser } = useFirebase()

  useEffect(() => {
    if (!isAuthenticated) {
      return
    }
    setUser(firebaseUser)
    setIsLoading(false)
  }, [isAuthenticated, firebaseUser])

  const refreshToken = async () => {
    if (user) {
      authStorage.authToken = await user.getIdToken(true)
    }
  }

  useEffect(() => {
    try {
      const interval = setInterval(refreshToken, 50 * 60)
      return () => clearInterval(interval)
    } catch (error) {
      console.error(error)
      return () => {}
    }
  }, [])

  useEffect(() => {
    const loadUserData = async () => {
      if (user) {
        // User is authenticated, get Firebase ID token and load user data
        try {
          authStorage.authToken = await user.getIdToken(true)
          const res: UserDataLoaderOutput = await trpc.loggedInUser.query()
          if (res) {
            setUserData(res)
          }
        } catch (error) {
          setUserLoaded()
        }
      } else if (!isLoading) {
        // User is not authenticated and loading is complete
        setUserLoaded()
        clearUserData()
      }
    }

    if (!isLoading) {
      loadUserData()
    }
    // eslint-disable-next-line react-hooks-addons/no-unused-deps
  }, [user, isLoading, setUserData, setUserLoaded, fetchCount])

  const reFetch = useCallback(async () => {
    setFetchCount(fetchCount + 1)
  }, [setFetchCount, fetchCount])

  return { isLoading, user, reFetch }
}
