import { Box, FormControl, TextField, Typography } from "@mui/material"
import React, { ChangeEvent, FC } from "react"
import { Controller, useFormContext } from "react-hook-form"

interface FormInputProps {
  label: string
  name: string
  placeholder?: string
  helper?: string
  required?: boolean
  multiline?: boolean
  numeric?: boolean
  type?: "password" | "text" | "number" | "email"
}

export const FormInput: FC<FormInputProps> = ({
  label,
  name,
  placeholder,
  helper = "",
  type = "",
  required = false,
  multiline = false,
  numeric = false,
}) => {
  const { control } = useFormContext()
  return (
    <Controller
      name={name}
      control={control}
      rules={{ required }}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <>
          <FormControl error={!!error}>
            <TextField
              helperText={error ? error.message : null}
              size="small"
              error={!!error}
              onChange={(event: ChangeEvent<HTMLInputElement>) =>
                onChange(
                  numeric ? parseInt(event.target.value) : event.target.value,
                )
              }
              placeholder={placeholder}
              type={type ? type : numeric ? "number" : "text"}
              value={value}
              fullWidth
              multiline={multiline}
              rows={multiline ? 4 : 1}
              label={label}
              variant="outlined"
            />
          </FormControl>
          {helper && (
            <Box>
              <Typography variant="caption" color="textSecondary">
                {helper}
              </Typography>
            </Box>
          )}
        </>
      )}
    />
  )
}
