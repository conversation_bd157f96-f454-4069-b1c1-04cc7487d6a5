import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import {
  Alert,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
} from "@mui/material"
import { createUserWithEmailAndPassword, getAuth } from "firebase/auth"
import { memo, useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON>ider, type SubmitHandler, useForm } from "react-hook-form"
import { z } from "zod"

import { useModalStore } from "../../../store/useModalStore"
import { firebaseApp } from "../../../utils/firebase"
import { FormInput } from "../../elements/HookElements/FormInput"

export const REGISTER_MODAL_NAME = "register"

export const loginInputSchema = z
  .object({
    email: z.string().email(),
    password: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z
      .string()
      .min(8, "Confirm Password must be at least 8 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"], // This will attach the error to confirmPassword
  })

type LoginInput = z.infer<typeof loginInputSchema>

export const RegisterModal = memo(() => {
  const [error, setError] = useState<string | null>(null)

  const auth = getAuth(firebaseApp)

  // Only select the isOpen value to prevent unnecessary rerenders
  const isOpen = useModalStore(
    (state) => state.modalList[REGISTER_MODAL_NAME]?.isOpen ?? false,
  )

  // Get functions separately to avoid including them in the reactive selector
  const { createModal, closeModal } = useModalStore()

  const methods = useForm<LoginInput>({
    resolver: zodResolver(loginInputSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  useEffect(() => {
    createModal(REGISTER_MODAL_NAME)
  }, [createModal])

  const handleEmailRegister: SubmitHandler<LoginInput> = async (data) => {
    try {
      await createUserWithEmailAndPassword(auth, data.email, data.password)
      closeModal(REGISTER_MODAL_NAME)
    } catch (errorMessage: unknown) {
      const error = errorMessage as { message: string }
      setError(error.message ?? "Unknown error")
      console.error("Login failed:", error)
    }
  }

  if (!isOpen) {
    return null
  }

  return (
    <Dialog open={isOpen} onClose={() => closeModal(REGISTER_MODAL_NAME)}>
      <DialogTitle>Register</DialogTitle>
      <DialogContent>
        <Box
          display="flex"
          justifyContent="center"
          flexDirection="column"
          gap={4}
          alignItems="center"
        >
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(handleEmailRegister)}>
              <Box display="flex" flexDirection="column" gap={2}>
                <FormInput
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                />
                <FormInput label="Password" type="password" name="password" />
                <FormInput
                  label="Confirm Password"
                  name="confirmPassword"
                  type="password"
                />
                {error && <Alert color="error">{error}</Alert>}
                <Button type="submit" variant="contained" color="primary">
                  Register
                </Button>
              </Box>
            </form>
          </FormProvider>
        </Box>
      </DialogContent>
    </Dialog>
  )
})
