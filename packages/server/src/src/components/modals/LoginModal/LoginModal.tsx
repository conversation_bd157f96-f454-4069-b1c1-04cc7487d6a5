import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
} from "@mui/material"
import {
  GoogleAuthProvider,
  getAuth,
  signInWithEmailAndPassword,
  signInWithPopup,
} from "firebase/auth"
import { memo, useCallback, useEffect, useState } from "react"
import { FormProvider, type SubmitHandler, useForm } from "react-hook-form"
import { z } from "zod"

import { useModalStore } from "../../../store/useModalStore"
import { firebaseApp } from "../../../utils/firebase"
import { FormInput } from "../../elements/HookElements/FormInput"
import { REGISTER_MODAL_NAME } from "../RegisterModal/RegisterModal"

export const LOGIN_MODAL_NAME = "login"

export const loginInputSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
})

type LoginInput = z.infer<typeof loginInputSchema>

export const LoginModal = memo(() => {
  const [error, setError] = useState<string | null>(null)

  const auth = getAuth(firebaseApp)

  // Only select the isOpen value to prevent unnecessary rerenders
  const isOpen = useModalStore(
    (state) => state.modalList[LOGIN_MODAL_NAME]?.isOpen ?? false,
  )

  // Get functions separately to avoid including them in the reactive selector
  const { createModal, closeModal, openModal } = useModalStore()

  const methods = useForm<LoginInput>({
    resolver: zodResolver(loginInputSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  useEffect(() => {
    createModal(LOGIN_MODAL_NAME)
  }, [createModal])

  const handleGoogleLogin = useCallback(async () => {
    const provider = new GoogleAuthProvider()
    try {
      await signInWithPopup(auth, provider)
    } catch (error) {
      console.error("Login failed:", error)
    }
    closeModal(LOGIN_MODAL_NAME)
  }, [auth, closeModal])

  const handleEmailLogin: SubmitHandler<LoginInput> = async (data) => {
    try {
      await signInWithEmailAndPassword(auth, data.email, data.password)
      closeModal(LOGIN_MODAL_NAME)
    } catch (errorMessage: unknown) {
      const error = errorMessage as { message: string }
      setError(error.message ?? "Unknown error")
      console.error("Login failed:", error)
    }
  }

  const handleRegister = () => {
    closeModal(LOGIN_MODAL_NAME)
    openModal(REGISTER_MODAL_NAME)
  }

  if (!isOpen) {
    return null
  }

  return (
    <Dialog open={isOpen} onClose={() => closeModal(LOGIN_MODAL_NAME)}>
      <DialogTitle>Login</DialogTitle>
      <DialogContent>
        <Box
          display="flex"
          justifyContent="center"
          flexDirection="column"
          gap={4}
          alignItems="center"
        >
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleGoogleLogin()}
          >
            Sign on/Log In With Google
          </Button>

          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(handleEmailLogin)}>
              <Box display="flex" flexDirection="column" gap={2}>
                <FormInput
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                />
                <FormInput label="Password" name="password" type="password" />
                {error && <Alert color="error">{error}</Alert>}
                <Button type="submit" variant="outlined" color="primary">
                  Login
                </Button>
              </Box>
            </form>
          </FormProvider>

          <Button onClick={handleRegister} variant="text" color="primary">
            Register
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  )
})
