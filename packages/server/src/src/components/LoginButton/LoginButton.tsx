import { Button } from "@mui/material"
import { getAuth, signOut } from "firebase/auth"

import { useFirebase } from "../../hooks/useFirebase"
import { useModalStore } from "../../store/useModalStore"
import { trpc } from "../../trpc/trpc"
import { firebaseApp } from "../../utils/firebase"
import { LOGIN_MODAL_NAME } from "../modals/LoginModal/LoginModal"

export const LoginButton = () => {
  const openModal = useModalStore((state) => state.openModal)
  const { isAuthenticated } = useFirebase()
  const auth = getAuth(firebaseApp)

  const handleLogin = async () => {
    openModal(LOGIN_MODAL_NAME)
  }

  const handleLogout = async () => {
    try {
      await trpc.logoutUser.query()
      await signOut(auth)
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  if (isAuthenticated) {
    return (
      <Button variant="outlined" color="inherit" onClick={() => handleLogout()}>
        Logout
      </Button>
    )
  }

  return (
    <Button variant="outlined" color="inherit" onClick={() => handleLogin()}>
      Sign on/Log In
    </Button>
  )
}
